#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PNG图片智能描边工具 - GUI界面
提供简单易用的图形界面
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, colorchooser
from PIL import Image, ImageTk
import os
import threading
from 高级描边工具 import AdvancedBorderTool, BorderMode

class BorderToolGUI:
    """描边工具GUI类"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("PNG图片智能描边工具")
        self.root.geometry("800x600")
        
        # 初始化工具
        self.border_tool = AdvancedBorderTool()
        
        # 变量
        self.input_path = tk.StringVar()
        self.output_path = tk.StringVar()
        self.border_thickness = tk.IntVar(value=3)
        self.border_color = (255, 255, 255)
        self.border_mode = tk.StringVar(value="solid")
        self.auto_thickness = tk.BooleanVar(value=False)
        self.smooth_level = tk.IntVar(value=2)
        self.feather_radius = tk.IntVar(value=1)
        
        # 创建界面
        self.create_widgets()
        
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="文件选择", padding="10")
        file_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 输入文件
        ttk.Label(file_frame, text="输入图片:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(file_frame, textvariable=self.input_path, width=50).grid(row=0, column=1, padx=(5, 5), pady=2)
        ttk.Button(file_frame, text="浏览", command=self.browse_input).grid(row=0, column=2, pady=2)
        
        # 输出文件
        ttk.Label(file_frame, text="输出图片:").grid(row=1, column=0, sticky=tk.W, pady=2)
        ttk.Entry(file_frame, textvariable=self.output_path, width=50).grid(row=1, column=1, padx=(5, 5), pady=2)
        ttk.Button(file_frame, text="浏览", command=self.browse_output).grid(row=1, column=2, pady=2)
        
        # 参数设置区域
        param_frame = ttk.LabelFrame(main_frame, text="描边参数", padding="10")
        param_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N), padx=(0, 5))
        
        # 描边厚度
        ttk.Label(param_frame, text="描边厚度:").grid(row=0, column=0, sticky=tk.W, pady=2)
        thickness_scale = ttk.Scale(param_frame, from_=1, to=10, variable=self.border_thickness, 
                                   orient=tk.HORIZONTAL, length=200)
        thickness_scale.grid(row=0, column=1, padx=(5, 0), pady=2)
        ttk.Label(param_frame, textvariable=self.border_thickness).grid(row=0, column=2, padx=(5, 0), pady=2)
        
        # 自动厚度
        ttk.Checkbutton(param_frame, text="自动检测最佳厚度", 
                       variable=self.auto_thickness).grid(row=1, column=0, columnspan=3, sticky=tk.W, pady=2)
        
        # 描边颜色
        ttk.Label(param_frame, text="描边颜色:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.color_button = ttk.Button(param_frame, text="选择颜色", command=self.choose_color)
        self.color_button.grid(row=2, column=1, sticky=tk.W, padx=(5, 0), pady=2)
        
        # 描边模式
        ttk.Label(param_frame, text="描边模式:").grid(row=3, column=0, sticky=tk.W, pady=2)
        mode_combo = ttk.Combobox(param_frame, textvariable=self.border_mode, 
                                 values=["solid", "gradient", "shadow", "glow"], state="readonly")
        mode_combo.grid(row=3, column=1, sticky=tk.W, padx=(5, 0), pady=2)
        
        # 平滑级别
        ttk.Label(param_frame, text="平滑级别:").grid(row=4, column=0, sticky=tk.W, pady=2)
        smooth_scale = ttk.Scale(param_frame, from_=0, to=5, variable=self.smooth_level, 
                                orient=tk.HORIZONTAL, length=200)
        smooth_scale.grid(row=4, column=1, padx=(5, 0), pady=2)
        ttk.Label(param_frame, textvariable=self.smooth_level).grid(row=4, column=2, padx=(5, 0), pady=2)
        
        # 羽化半径
        ttk.Label(param_frame, text="羽化半径:").grid(row=5, column=0, sticky=tk.W, pady=2)
        feather_scale = ttk.Scale(param_frame, from_=0, to=5, variable=self.feather_radius, 
                                 orient=tk.HORIZONTAL, length=200)
        feather_scale.grid(row=5, column=1, padx=(5, 0), pady=2)
        ttk.Label(param_frame, textvariable=self.feather_radius).grid(row=5, column=2, padx=(5, 0), pady=2)
        
        # 预览区域
        preview_frame = ttk.LabelFrame(main_frame, text="图片预览", padding="10")
        preview_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 0))
        
        # 预览画布
        self.preview_canvas = tk.Canvas(preview_frame, width=300, height=400, bg="white")
        self.preview_canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 滚动条
        v_scrollbar = ttk.Scrollbar(preview_frame, orient=tk.VERTICAL, command=self.preview_canvas.yview)
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.preview_canvas.configure(yscrollcommand=v_scrollbar.set)
        
        h_scrollbar = ttk.Scrollbar(preview_frame, orient=tk.HORIZONTAL, command=self.preview_canvas.xview)
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        self.preview_canvas.configure(xscrollcommand=h_scrollbar.set)
        
        # 控制按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, columnspan=2, pady=(10, 0))
        
        ttk.Button(button_frame, text="预览效果", command=self.preview_border).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="开始处理", command=self.process_image).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="重置参数", command=self.reset_params).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="退出", command=self.root.quit).pack(side=tk.LEFT, padx=(5, 0))
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(5, 0))
        
        # 配置网格权重
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        preview_frame.columnconfigure(0, weight=1)
        preview_frame.rowconfigure(0, weight=1)
        
    def browse_input(self):
        """浏览输入文件"""
        filename = filedialog.askopenfilename(
            title="选择PNG图片",
            filetypes=[("PNG files", "*.png"), ("All files", "*.*")]
        )
        if filename:
            self.input_path.set(filename)
            self.load_preview()
            
            # 自动设置输出路径
            if not self.output_path.get():
                name, ext = os.path.splitext(filename)
                self.output_path.set(f"{name}_描边{ext}")
    
    def browse_output(self):
        """浏览输出文件"""
        filename = filedialog.asksaveasfilename(
            title="保存描边图片",
            defaultextension=".png",
            filetypes=[("PNG files", "*.png"), ("All files", "*.*")]
        )
        if filename:
            self.output_path.set(filename)
    
    def choose_color(self):
        """选择描边颜色"""
        color = colorchooser.askcolor(title="选择描边颜色", initialcolor=self.border_color)
        if color[0]:
            self.border_color = tuple(int(c) for c in color[0])
            # 更新按钮颜色显示
            color_hex = "#{:02x}{:02x}{:02x}".format(*self.border_color)
            self.color_button.configure(text=f"颜色: {color_hex}")
    
    def load_preview(self):
        """加载图片预览"""
        if not self.input_path.get() or not os.path.exists(self.input_path.get()):
            return
            
        try:
            # 加载图片
            img = Image.open(self.input_path.get())
            
            # 缩放到合适大小
            max_size = (280, 380)
            img.thumbnail(max_size, Image.Resampling.LANCZOS)
            
            # 转换为PhotoImage
            self.preview_image = ImageTk.PhotoImage(img)
            
            # 显示在画布上
            self.preview_canvas.delete("all")
            self.preview_canvas.create_image(150, 200, image=self.preview_image)
            
            # 更新滚动区域
            self.preview_canvas.configure(scrollregion=self.preview_canvas.bbox("all"))
            
            self.status_var.set(f"已加载图片: {img.size}")
            
        except Exception as e:
            messagebox.showerror("错误", f"加载图片失败: {e}")
    
    def preview_border(self):
        """预览描边效果"""
        if not self.input_path.get():
            messagebox.showwarning("警告", "请先选择输入图片")
            return
            
        self.status_var.set("正在生成预览...")
        self.progress.start()
        
        # 在后台线程中处理
        threading.Thread(target=self._preview_worker, daemon=True).start()
    
    def _preview_worker(self):
        """预览工作线程"""
        try:
            # 创建临时输出文件
            temp_output = "temp_preview.png"
            
            # 设置参数
            border_color = self.border_color + (255,)
            border_mode = BorderMode(self.border_mode.get())
            
            # 处理图片
            success = self.border_tool.process_image(
                self.input_path.get(),
                temp_output,
                border_thickness=self.border_thickness.get(),
                border_color=border_color,
                border_mode=border_mode,
                auto_thickness=self.auto_thickness.get(),
                smooth_level=self.smooth_level.get(),
                feather_radius=self.feather_radius.get()
            )
            
            if success:
                # 在主线程中更新预览
                self.root.after(0, self._update_preview, temp_output)
            else:
                self.root.after(0, lambda: messagebox.showerror("错误", "预览生成失败"))
                
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"预览失败: {e}"))
        finally:
            self.root.after(0, self._preview_complete)
    
    def _update_preview(self, temp_file):
        """更新预览显示"""
        try:
            if os.path.exists(temp_file):
                # 加载预览图片
                img = Image.open(temp_file)
                
                # 缩放
                max_size = (280, 380)
                img.thumbnail(max_size, Image.Resampling.LANCZOS)
                
                # 更新显示
                self.preview_image = ImageTk.PhotoImage(img)
                self.preview_canvas.delete("all")
                self.preview_canvas.create_image(150, 200, image=self.preview_image)
                
                # 清理临时文件
                os.remove(temp_file)
                
                self.status_var.set("预览已更新")
        except Exception as e:
            messagebox.showerror("错误", f"更新预览失败: {e}")
    
    def _preview_complete(self):
        """预览完成"""
        self.progress.stop()
    
    def process_image(self):
        """处理图片"""
        if not self.input_path.get():
            messagebox.showwarning("警告", "请先选择输入图片")
            return
            
        if not self.output_path.get():
            messagebox.showwarning("警告", "请设置输出路径")
            return
        
        self.status_var.set("正在处理图片...")
        self.progress.start()
        
        # 在后台线程中处理
        threading.Thread(target=self._process_worker, daemon=True).start()
    
    def _process_worker(self):
        """处理工作线程"""
        try:
            # 设置参数
            border_color = self.border_color + (255,)
            border_mode = BorderMode(self.border_mode.get())
            
            # 处理图片
            success = self.border_tool.process_image(
                self.input_path.get(),
                self.output_path.get(),
                border_thickness=self.border_thickness.get(),
                border_color=border_color,
                border_mode=border_mode,
                auto_thickness=self.auto_thickness.get(),
                smooth_level=self.smooth_level.get(),
                feather_radius=self.feather_radius.get()
            )
            
            if success:
                self.root.after(0, lambda: messagebox.showinfo("成功", f"描边完成！\n输出文件: {self.output_path.get()}"))
                self.root.after(0, lambda: self.status_var.set("处理完成"))
            else:
                self.root.after(0, lambda: messagebox.showerror("错误", "图片处理失败"))
                self.root.after(0, lambda: self.status_var.set("处理失败"))
                
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"处理失败: {e}"))
            self.root.after(0, lambda: self.status_var.set("处理失败"))
        finally:
            self.root.after(0, self._process_complete)
    
    def _process_complete(self):
        """处理完成"""
        self.progress.stop()
    
    def reset_params(self):
        """重置参数"""
        self.border_thickness.set(3)
        self.border_color = (255, 255, 255)
        self.border_mode.set("solid")
        self.auto_thickness.set(False)
        self.smooth_level.set(2)
        self.feather_radius.set(1)
        self.color_button.configure(text="选择颜色")
        self.status_var.set("参数已重置")

def main():
    """主函数"""
    root = tk.Tk()
    app = BorderToolGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
