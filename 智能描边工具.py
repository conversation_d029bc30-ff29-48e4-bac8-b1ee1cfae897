#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PNG图片智能描边功能
实现对已抠图PNG图片的智能描边功能
"""

import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFilter
import argparse
import os

class SmartBorderTool:
    """智能描边工具类"""
    
    def __init__(self):
        self.border_thickness = 3  # 默认描边厚度
        self.border_color = (255, 255, 255, 255)  # 默认白色描边
        self.smooth_iterations = 2  # 平滑迭代次数
    
    def load_image(self, image_path):
        """加载PNG图片"""
        try:
            img = Image.open(image_path).convert("RGBA")
            print(f"成功加载图片: {image_path}")
            print(f"图片尺寸: {img.size}")
            return img
        except Exception as e:
            print(f"加载图片失败: {e}")
            return None
    
    def create_border_mask(self, alpha_channel):
        """创建描边遮罩"""
        # 转换为numpy数组
        alpha_array = np.array(alpha_channel)
        
        # 创建二值化遮罩（非透明区域）
        object_mask = (alpha_array > 0).astype(np.uint8)
        
        # 使用形态学膨胀操作创建描边区域
        kernel = np.ones((3, 3), np.uint8)
        
        # 膨胀操作，扩展边界
        dilated_mask = cv2.dilate(object_mask, kernel, iterations=self.border_thickness)
        
        # 描边区域 = 膨胀后的区域 - 原始对象区域
        border_mask = dilated_mask - object_mask
        
        return border_mask, object_mask
    
    def smooth_border(self, border_mask):
        """平滑描边边缘"""
        # 使用高斯模糊平滑边缘
        smoothed = cv2.GaussianBlur(border_mask.astype(np.float32), (5, 5), 1.0)
        
        # 使用形态学操作进一步平滑
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        
        for _ in range(self.smooth_iterations):
            smoothed = cv2.morphologyEx(smoothed, cv2.MORPH_CLOSE, kernel)
            smoothed = cv2.morphologyEx(smoothed, cv2.MORPH_OPEN, kernel)
        
        return (smoothed * 255).astype(np.uint8)
    
    def apply_border(self, original_img):
        """应用描边效果"""
        # 获取原图的RGBA通道
        r, g, b, a = original_img.split()
        
        # 创建描边遮罩
        border_mask, object_mask = self.create_border_mask(a)
        
        # 平滑描边
        border_mask = self.smooth_border(border_mask)
        
        # 创建新的图片，尺寸需要考虑描边扩展
        padding = self.border_thickness * 2
        new_width = original_img.width + padding
        new_height = original_img.height + padding
        
        # 创建新的RGBA图片
        result_img = Image.new("RGBA", (new_width, new_height), (0, 0, 0, 0))
        
        # 将原图粘贴到中心位置
        offset = self.border_thickness
        result_img.paste(original_img, (offset, offset), original_img)
        
        # 创建描边层
        border_layer = Image.new("RGBA", (new_width, new_height), (0, 0, 0, 0))
        border_array = np.array(border_layer)
        
        # 在正确位置应用描边
        border_region = border_array[offset:offset+original_img.height, 
                                   offset:offset+original_img.width]
        
        # 设置描边颜色
        border_region[:, :, 0] = self.border_color[0]  # R
        border_region[:, :, 1] = self.border_color[1]  # G  
        border_region[:, :, 2] = self.border_color[2]  # B
        border_region[:, :, 3] = border_mask  # A (使用描边遮罩作为透明度)
        
        # 更新描边层
        border_layer = Image.fromarray(border_array)
        
        # 合成最终图片：先绘制描边，再绘制原图
        final_img = Image.alpha_composite(border_layer, result_img)
        
        return final_img
    
    def process_image(self, input_path, output_path=None, 
                     border_thickness=3, border_color=(255, 255, 255, 255)):
        """处理图片添加描边"""
        # 设置参数
        self.border_thickness = border_thickness
        self.border_color = border_color
        
        # 加载图片
        img = self.load_image(input_path)
        if img is None:
            return False
        
        # 应用描边
        print("正在应用描边效果...")
        result_img = self.apply_border(img)
        
        # 保存结果
        if output_path is None:
            name, ext = os.path.splitext(input_path)
            output_path = f"{name}_描边{ext}"
        
        try:
            result_img.save(output_path, "PNG")
            print(f"描边完成！结果已保存为: {output_path}")
            return True
        except Exception as e:
            print(f"保存图片失败: {e}")
            return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="PNG图片智能描边工具")
    parser.add_argument("input", help="输入PNG图片路径")
    parser.add_argument("-o", "--output", help="输出图片路径")
    parser.add_argument("-t", "--thickness", type=int, default=3, help="描边厚度（默认3像素）")
    parser.add_argument("-c", "--color", nargs=3, type=int, default=[255, 255, 255], 
                       help="描边颜色RGB值（默认白色）")
    
    args = parser.parse_args()
    
    # 创建描边工具
    tool = SmartBorderTool()
    
    # 处理图片
    border_color = tuple(args.color + [255])  # 添加Alpha通道
    success = tool.process_image(
        args.input, 
        args.output, 
        args.thickness, 
        border_color
    )
    
    if success:
        print("描边处理完成！")
    else:
        print("描边处理失败！")

if __name__ == "__main__":
    # 如果没有命令行参数，使用示例图片进行测试
    import sys
    if len(sys.argv) == 1:
        print("=== PNG图片智能描边工具测试 ===")
        tool = SmartBorderTool()
        
        # 测试描边前图片
        if os.path.exists("描边前.png"):
            print("正在处理测试图片...")
            tool.process_image("描边前.png", "测试描边结果.png", 
                             border_thickness=3, border_color=(255, 255, 255, 255))
        else:
            print("未找到测试图片 '描边前.png'")
    else:
        main()
