#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PNG图片智能描边工具 - 安装脚本
自动检查和安装所需依赖库
"""

import subprocess
import sys
import os

def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    version = sys.version_info
    
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print(f"❌ Python版本过低: {version.major}.{version.minor}")
        print("   需要Python 3.7或更高版本")
        return False
    
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True

def install_package(package_name):
    """安装单个包"""
    try:
        print(f"📦 正在安装 {package_name}...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package_name
        ], capture_output=True, text=True, check=True)
        
        print(f"✅ {package_name} 安装成功")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} 安装失败:")
        print(f"   错误信息: {e.stderr}")
        return False

def check_and_install_dependencies():
    """检查并安装依赖库"""
    print("\n📋 检查依赖库...")
    
    # 定义依赖包
    dependencies = {
        'opencv-python': 'cv2',
        'Pillow': 'PIL',
        'numpy': 'numpy',
        'matplotlib': 'matplotlib'
    }
    
    missing_packages = []
    
    # 检查每个依赖
    for package, module in dependencies.items():
        try:
            __import__(module)
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"❌ {package} 未安装")
            missing_packages.append(package)
    
    # 安装缺失的包
    if missing_packages:
        print(f"\n🔧 需要安装 {len(missing_packages)} 个依赖包...")
        
        for package in missing_packages:
            if not install_package(package):
                return False
        
        print("\n🎉 所有依赖库安装完成！")
    else:
        print("\n✅ 所有依赖库已安装")
    
    return True

def check_tkinter():
    """检查tkinter是否可用"""
    print("\n🖥️ 检查GUI支持...")
    
    try:
        import tkinter as tk
        print("✅ tkinter 可用，支持图形界面")
        return True
    except ImportError:
        print("❌ tkinter 不可用，无法使用图形界面")
        print("   建议安装完整版Python或使用命令行模式")
        return False

def create_desktop_shortcut():
    """创建桌面快捷方式（Windows）"""
    if sys.platform.startswith('win'):
        try:
            import winshell
            from win32com.client import Dispatch
            
            desktop = winshell.desktop()
            path = os.path.join(desktop, "PNG描边工具.lnk")
            target = os.path.join(os.getcwd(), "主程序.py")
            wDir = os.getcwd()
            icon = target
            
            shell = Dispatch('WScript.Shell')
            shortcut = shell.CreateShortCut(path)
            shortcut.Targetpath = sys.executable
            shortcut.Arguments = f'"{target}"'
            shortcut.WorkingDirectory = wDir
            shortcut.IconLocation = icon
            shortcut.save()
            
            print("✅ 桌面快捷方式已创建")
            return True
            
        except ImportError:
            print("ℹ️ 无法创建桌面快捷方式（需要pywin32）")
            return False
        except Exception as e:
            print(f"⚠️ 创建快捷方式失败: {e}")
            return False
    else:
        print("ℹ️ 非Windows系统，跳过快捷方式创建")
        return False

def run_quick_test():
    """运行快速测试"""
    print("\n🧪 运行快速测试...")
    
    # 检查测试图片
    if not os.path.exists("描边前.png"):
        print("❌ 未找到测试图片 '描边前.png'")
        print("   请将PNG图片放在程序目录并命名为 '描边前.png'")
        return False
    
    try:
        from 主程序 import quick_test
        quick_test()
        print("✅ 快速测试完成")
        return True
    except Exception as e:
        print(f"❌ 快速测试失败: {e}")
        return False

def main():
    """主安装流程"""
    print("=" * 60)
    print("🎨 PNG图片智能描边工具 - 安装向导")
    print("=" * 60)
    
    # 检查Python版本
    if not check_python_version():
        input("按回车键退出...")
        return
    
    # 检查并安装依赖
    if not check_and_install_dependencies():
        print("❌ 依赖安装失败，请手动安装")
        input("按回车键退出...")
        return
    
    # 检查GUI支持
    gui_available = check_tkinter()
    
    # 创建快捷方式
    create_desktop_shortcut()
    
    # 运行测试
    if os.path.exists("描边前.png"):
        test_choice = input("\n🧪 是否运行快速测试？(y/N): ").strip().lower()
        if test_choice == 'y':
            run_quick_test()
    
    # 显示完成信息
    print("\n" + "=" * 60)
    print("🎉 安装完成！")
    print("=" * 60)
    print("📋 使用方式:")
    
    if gui_available:
        print("  1. 图形界面: python 主程序.py")
        print("  2. 直接GUI: python 描边工具GUI.py")
    
    print("  3. 命令行: python 高级描边工具.py input.png")
    print("  4. 批量处理: python 批量描边工具.py /path/to/images")
    
    print("\n📖 详细说明请查看:")
    print("  - README.md (项目概述)")
    print("  - 使用说明.md (详细使用方法)")
    print("  - 项目说明.md (技术架构)")
    
    print("\n🎯 快速开始:")
    print("  1. 将PNG图片放在程序目录")
    print("  2. 运行: python 主程序.py")
    print("  3. 选择图形界面模式")
    print("  4. 享受完美的描边效果！")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
