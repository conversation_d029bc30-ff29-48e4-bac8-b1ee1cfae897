# 🎨 PNG图片智能描边工具

一个专为已抠图PNG图片设计的智能描边工具，能够为透明背景的PNG图片添加完美的封闭描边效果。

## ✨ 功能特点

- 🎯 **智能边缘检测** - 自动识别主体对象的边缘轮廓
- 🔒 **完美封闭描边** - 生成无断点、无缺失的连续描边
- 🌟 **保持透明背景** - 完全保留原图的透明特性
- 🎨 **多种描边模式** - 支持实心、渐变、阴影、发光等效果
- 🤖 **自动厚度检测** - 根据图片复杂度智能推荐描边厚度
- 🖥️ **图形界面** - 提供直观易用的GUI界面
- ⚡ **批量处理** - 支持批量处理多个文件
- 🛠️ **丰富参数** - 厚度、颜色、平滑度、羽化等可调节

## 🚀 快速开始

### 安装依赖
```bash
pip install opencv-python pillow numpy matplotlib
```

### 运行主程序
```bash
python 主程序.py
```

### 快速测试
将PNG图片命名为 `描边前.png` 放在程序目录，然后运行：
```bash
python -c "from 主程序 import quick_test; quick_test()"
```

## 📋 使用方式

### 1. 图形界面模式（推荐）
```bash
python 描边工具GUI.py
```
- 直观的参数调节界面
- 实时预览功能
- 支持拖拽文件选择

### 2. 命令行模式
```bash
# 基础用法
python 高级描边工具.py input.png

# 完整参数
python 高级描边工具.py input.png -o output.png -t 4 -c 255 0 0 -m gradient --auto-thickness
```

### 3. 批量处理模式
```bash
python 批量描边工具.py /path/to/images -o /path/to/output -m solid -t 3
```

## 🎨 描边模式

| 模式 | 效果 | 适用场景 |
|------|------|----------|
| **solid** | 均匀实色描边 | 大多数场景，简洁明了 |
| **gradient** | 透明度渐变 | 需要柔和过渡效果 |
| **shadow** | 带偏移阴影 | 需要立体感的设计 |
| **glow** | 多层发光 | 需要醒目或梦幻效果 |

## 📁 项目结构

```
PNG图片智能描边工具/
├── 主程序.py              # 🚪 主程序入口
├── 智能描边工具.py         # 🔧 基础描边功能
├── 高级描边工具.py         # ⚡ 高级描边功能
├── 描边工具GUI.py          # 🖥️ 图形用户界面
├── 批量描边工具.py         # 📦 批量处理功能
├── 使用说明.md            # 📖 详细使用说明
├── 项目说明.md            # 🏗️ 技术架构说明
└── README.md              # 📋 项目概述
```

## 🛠️ 参数说明

### 基础参数
- **描边厚度**: 1-10像素，控制描边粗细
- **描边颜色**: RGB颜色值，如 `255 255 255` (白色)
- **描边模式**: solid/gradient/shadow/glow

### 高级参数
- **自动厚度**: 智能检测最适合的描边厚度
- **平滑级别**: 0-5级，数值越高边缘越平滑
- **羽化半径**: 0-5像素，创造柔和的边缘过渡

## 💡 使用技巧

### 参数选择建议

**简单图形**（几何形状）
- 厚度：2-3像素
- 模式：solid
- 平滑：1-2级

**复杂图形**（人物、动物）
- 厚度：3-5像素
- 模式：gradient或glow
- 平滑：2-3级
- 羽化：1-2像素

**精细图形**（文字、图标）
- 启用自动厚度检测
- 模式：solid
- 平滑：3-4级

### 颜色选择建议
- **白色描边**: 适用于深色背景
- **黑色描边**: 适用于浅色背景
- **对比色描边**: 突出主体对象
- **同色系描边**: 创造和谐效果

## 🔧 技术原理

### 核心算法
1. **边缘检测**: 基于Alpha通道的精确边缘识别
2. **形态学处理**: 使用膨胀操作扩展边界
3. **平滑优化**: 多级平滑算法确保描边质量
4. **效果渲染**: 根据模式应用不同的视觉效果

### 关键技术
- OpenCV形态学操作
- 距离变换算法
- 高斯模糊平滑
- Alpha通道合成

## 📊 性能特点

- ⚡ **高效处理**: 优化的算法确保快速处理
- 💾 **内存友好**: 智能内存管理，支持大图片
- 🔄 **并发支持**: 批量模式支持多线程处理
- 🎯 **质量保证**: 多级优化确保描边质量

## 🎯 示例效果

处理前后对比：

| 原图 | 实心描边 | 渐变描边 | 发光描边 |
|------|----------|----------|----------|
| ![原图](描边前.png) | ![实心](快速测试_solid.png) | ![渐变](快速测试_gradient.png) | ![发光](快速测试_glow.png) |

## ❓ 常见问题

**Q: 描边出现断点怎么办？**
A: 增加平滑级别，或者启用羽化效果。

**Q: 描边太粗或太细？**
A: 调整厚度参数，或启用自动厚度检测。

**Q: 处理后图片变大了？**
A: 这是正常的，描边需要额外空间。程序会自动扩展画布。

**Q: 支持哪些图片格式？**
A: 主要支持PNG格式，特别是带透明通道的PNG图片。

## 🔄 更新日志

### v2.0.0 (当前版本)
- ✅ 新增GUI界面
- ✅ 支持多种描边模式
- ✅ 智能厚度检测
- ✅ 预览功能
- ✅ 批量处理支持

### v1.0.0
- ✅ 基础描边功能
- ✅ 支持厚度和颜色调节

## 📄 许可证

本项目仅供学习和个人使用。

## 🤝 贡献

欢迎提交问题和改进建议！

---

**🎉 享受完美的描边效果！**
