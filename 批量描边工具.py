#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PNG图片批量智能描边工具
支持批量处理多个PNG文件
"""

import os
import glob
import argparse
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from 高级描边工具 import AdvancedBorderTool, BorderMode

class BatchBorderTool:
    """批量描边工具类"""
    
    def __init__(self):
        self.border_tool = AdvancedBorderTool()
        self.processed_count = 0
        self.failed_count = 0
        self.total_count = 0
        
    def find_png_files(self, input_dir, recursive=True):
        """查找PNG文件"""
        png_files = []
        
        if recursive:
            # 递归查找所有子目录中的PNG文件
            pattern = os.path.join(input_dir, "**", "*.png")
            png_files = glob.glob(pattern, recursive=True)
        else:
            # 只查找当前目录中的PNG文件
            pattern = os.path.join(input_dir, "*.png")
            png_files = glob.glob(pattern)
        
        # 过滤掉已经处理过的文件（避免重复处理）
        filtered_files = []
        for file in png_files:
            filename = os.path.basename(file)
            if not any(suffix in filename for suffix in ["_描边", "_solid_", "_gradient_", "_shadow_", "_glow_"]):
                filtered_files.append(file)
        
        return filtered_files
    
    def generate_output_path(self, input_path, output_dir, suffix="描边"):
        """生成输出文件路径"""
        filename = os.path.basename(input_path)
        name, ext = os.path.splitext(filename)
        
        if output_dir:
            # 如果指定了输出目录
            os.makedirs(output_dir, exist_ok=True)
            output_path = os.path.join(output_dir, f"{name}_{suffix}{ext}")
        else:
            # 在原文件同目录下生成
            input_dir = os.path.dirname(input_path)
            output_path = os.path.join(input_dir, f"{name}_{suffix}{ext}")
        
        return output_path
    
    def process_single_file(self, input_path, output_dir=None, **kwargs):
        """处理单个文件"""
        try:
            # 生成输出路径
            mode_name = kwargs.get('border_mode', BorderMode.SOLID).value
            output_path = self.generate_output_path(input_path, output_dir, f"{mode_name}_描边")
            
            # 检查输出文件是否已存在
            if os.path.exists(output_path) and not kwargs.get('overwrite', False):
                print(f"跳过已存在的文件: {output_path}")
                return True, f"跳过: {os.path.basename(input_path)}"
            
            # 处理图片
            success = self.border_tool.process_image(input_path, output_path, **kwargs)
            
            if success:
                return True, f"成功: {os.path.basename(input_path)} -> {os.path.basename(output_path)}"
            else:
                return False, f"失败: {os.path.basename(input_path)}"
                
        except Exception as e:
            return False, f"错误: {os.path.basename(input_path)} - {str(e)}"
    
    def process_batch(self, input_dir, output_dir=None, max_workers=4, **kwargs):
        """批量处理文件"""
        print("=== PNG图片批量智能描边工具 ===")
        print(f"输入目录: {input_dir}")
        print(f"输出目录: {output_dir or '原文件目录'}")
        
        # 查找PNG文件
        png_files = self.find_png_files(input_dir, kwargs.get('recursive', True))
        
        if not png_files:
            print("未找到PNG文件！")
            return
        
        self.total_count = len(png_files)
        print(f"找到 {self.total_count} 个PNG文件")
        
        # 显示处理参数
        print(f"\n处理参数:")
        print(f"  描边厚度: {kwargs.get('border_thickness', 3)}")
        print(f"  描边颜色: {kwargs.get('border_color', (255, 255, 255, 255))}")
        print(f"  描边模式: {kwargs.get('border_mode', BorderMode.SOLID).value}")
        print(f"  自动厚度: {kwargs.get('auto_thickness', False)}")
        print(f"  平滑级别: {kwargs.get('smooth_level', 2)}")
        print(f"  羽化半径: {kwargs.get('feather_radius', 1)}")
        print(f"  并发线程: {max_workers}")
        
        # 确认处理
        if not kwargs.get('auto_confirm', False):
            response = input(f"\n是否开始处理？(y/N): ")
            if response.lower() != 'y':
                print("已取消处理")
                return
        
        print(f"\n开始批量处理...")
        start_time = time.time()
        
        # 使用线程池并发处理
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_file = {
                executor.submit(self.process_single_file, file, output_dir, **kwargs): file 
                for file in png_files
            }
            
            # 处理完成的任务
            for future in as_completed(future_to_file):
                file_path = future_to_file[future]
                try:
                    success, message = future.result()
                    if success:
                        self.processed_count += 1
                    else:
                        self.failed_count += 1
                    
                    # 显示进度
                    progress = (self.processed_count + self.failed_count) / self.total_count * 100
                    print(f"[{progress:5.1f}%] {message}")
                    
                except Exception as e:
                    self.failed_count += 1
                    print(f"处理异常: {os.path.basename(file_path)} - {e}")
        
        # 显示统计结果
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        print(f"\n=== 处理完成 ===")
        print(f"总文件数: {self.total_count}")
        print(f"成功处理: {self.processed_count}")
        print(f"处理失败: {self.failed_count}")
        print(f"处理时间: {elapsed_time:.2f}秒")
        print(f"平均速度: {self.total_count/elapsed_time:.2f}文件/秒")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="PNG图片批量智能描边工具")
    
    # 基本参数
    parser.add_argument("input_dir", help="输入目录路径")
    parser.add_argument("-o", "--output-dir", help="输出目录路径（默认为原文件目录）")
    parser.add_argument("--no-recursive", action="store_true", help="不递归处理子目录")
    parser.add_argument("--overwrite", action="store_true", help="覆盖已存在的文件")
    parser.add_argument("--auto-confirm", action="store_true", help="自动确认，不询问")
    
    # 描边参数
    parser.add_argument("-t", "--thickness", type=int, default=3, help="描边厚度")
    parser.add_argument("-c", "--color", nargs=3, type=int, default=[255, 255, 255], help="描边颜色RGB")
    parser.add_argument("-m", "--mode", choices=["solid", "gradient", "shadow", "glow"], 
                       default="solid", help="描边模式")
    parser.add_argument("--auto-thickness", action="store_true", help="自动检测最佳厚度")
    parser.add_argument("--smooth", type=int, default=2, help="平滑级别")
    parser.add_argument("--feather", type=int, default=1, help="羽化半径")
    
    # 性能参数
    parser.add_argument("--workers", type=int, default=4, help="并发线程数")
    
    args = parser.parse_args()
    
    # 检查输入目录
    if not os.path.isdir(args.input_dir):
        print(f"错误: 输入目录不存在: {args.input_dir}")
        return
    
    # 创建批量处理工具
    batch_tool = BatchBorderTool()
    
    # 设置处理参数
    process_kwargs = {
        'border_thickness': args.thickness,
        'border_color': tuple(args.color + [255]),
        'border_mode': BorderMode(args.mode),
        'auto_thickness': args.auto_thickness,
        'smooth_level': args.smooth,
        'feather_radius': args.feather,
        'recursive': not args.no_recursive,
        'overwrite': args.overwrite,
        'auto_confirm': args.auto_confirm
    }
    
    # 开始批量处理
    batch_tool.process_batch(
        args.input_dir,
        args.output_dir,
        max_workers=args.workers,
        **process_kwargs
    )

if __name__ == "__main__":
    # 如果没有命令行参数，提供交互式模式
    import sys
    if len(sys.argv) == 1:
        print("=== PNG图片批量智能描边工具 - 交互模式 ===")
        
        # 获取输入目录
        input_dir = input("请输入PNG文件所在目录路径: ").strip()
        if not input_dir or not os.path.isdir(input_dir):
            print("无效的目录路径！")
            sys.exit(1)
        
        # 获取输出目录
        output_dir = input("请输入输出目录路径（回车使用原目录）: ").strip()
        if not output_dir:
            output_dir = None
        
        # 获取描边模式
        print("描边模式选择:")
        print("1. solid - 实心描边")
        print("2. gradient - 渐变描边") 
        print("3. shadow - 阴影描边")
        print("4. glow - 发光描边")
        
        mode_choice = input("请选择模式 (1-4, 默认1): ").strip()
        mode_map = {"1": "solid", "2": "gradient", "3": "shadow", "4": "glow"}
        mode = mode_map.get(mode_choice, "solid")
        
        # 创建工具并处理
        batch_tool = BatchBorderTool()
        
        process_kwargs = {
            'border_thickness': 3,
            'border_color': (255, 255, 255, 255),
            'border_mode': BorderMode(mode),
            'auto_thickness': True,
            'smooth_level': 2,
            'feather_radius': 1,
            'recursive': True,
            'overwrite': False,
            'auto_confirm': False
        }
        
        batch_tool.process_batch(input_dir, output_dir, max_workers=4, **process_kwargs)
    else:
        main()
