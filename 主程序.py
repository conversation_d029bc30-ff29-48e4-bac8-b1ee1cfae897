#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PNG图片智能描边工具 - 主程序入口
整合所有功能，提供统一的程序入口
"""

import sys
import os
import argparse
from pathlib import Path

def check_dependencies():
    """检查依赖库是否安装"""
    required_packages = {
        'cv2': 'opencv-python',
        'PIL': 'Pillow', 
        'numpy': 'numpy',
        'matplotlib': 'matplotlib'
    }
    
    missing_packages = []
    
    for module, package in required_packages.items():
        try:
            __import__(module)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ 缺少以下依赖库:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有依赖库已安装")
    return True

def show_welcome():
    """显示欢迎信息"""
    print("=" * 60)
    print("🎨 PNG图片智能描边工具")
    print("=" * 60)
    print("功能特点:")
    print("  • 智能边缘检测，生成完美封闭描边")
    print("  • 支持多种描边模式：实心、渐变、阴影、发光")
    print("  • 自动厚度检测，适应不同复杂度的图片")
    print("  • 保持透明背景，支持PNG格式")
    print("  • 提供GUI界面和命令行两种使用方式")
    print("  • 支持批量处理多个文件")
    print("=" * 60)

def show_menu():
    """显示主菜单"""
    print("\n📋 请选择使用模式:")
    print("1. 🖥️  图形界面模式 (推荐)")
    print("2. ⌨️  命令行模式")
    print("3. 📦 批量处理模式")
    print("4. 🧪 快速测试模式")
    print("5. 📖 查看使用说明")
    print("6. ❌ 退出程序")
    print("-" * 40)

def launch_gui():
    """启动GUI界面"""
    try:
        print("🚀 启动图形界面...")
        
        # 检查tkinter是否可用
        try:
            import tkinter as tk
        except ImportError:
            print("❌ tkinter未安装，无法启动图形界面")
            print("请安装tkinter或使用命令行模式")
            return False
        
        # 导入并启动GUI
        from 描边工具GUI import main as gui_main
        gui_main()
        return True
        
    except Exception as e:
        print(f"❌ 启动GUI失败: {e}")
        return False

def launch_cli():
    """启动命令行模式"""
    print("⌨️  命令行模式")
    print("请输入参数，或输入 'help' 查看帮助")
    
    while True:
        try:
            user_input = input("\n> ").strip()
            
            if not user_input:
                continue
            elif user_input.lower() in ['exit', 'quit', 'q']:
                break
            elif user_input.lower() == 'help':
                show_cli_help()
                continue
            
            # 解析用户输入
            args = user_input.split()
            
            # 检查输入文件是否存在
            if not args or not os.path.exists(args[0]):
                print("❌ 请输入有效的PNG文件路径")
                continue
            
            # 导入并使用高级描边工具
            from 高级描边工具 import main as cli_main
            
            # 模拟命令行参数
            sys.argv = ['高级描边工具.py'] + args
            cli_main()
            
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 处理失败: {e}")

def show_cli_help():
    """显示命令行帮助"""
    print("\n📖 命令行模式帮助:")
    print("基础用法:")
    print("  input.png                           # 使用默认参数处理")
    print("  input.png -o output.png             # 指定输出文件")
    print("  input.png -t 5 -c 255 0 0           # 5像素红色描边")
    print("  input.png -m gradient --auto-thickness  # 渐变模式，自动厚度")
    print("\n参数说明:")
    print("  -o, --output     输出文件路径")
    print("  -t, --thickness  描边厚度 (1-10)")
    print("  -c, --color      RGB颜色值")
    print("  -m, --mode       描边模式 (solid/gradient/shadow/glow)")
    print("  --auto-thickness 自动检测厚度")
    print("  --smooth         平滑级别 (0-5)")
    print("  --feather        羽化半径 (0-5)")
    print("\n输入 'exit' 或 'quit' 退出")

def launch_batch():
    """启动批量处理模式"""
    print("📦 批量处理模式")
    
    try:
        from 批量描边工具 import BatchBorderTool, BorderMode
        
        # 交互式获取参数
        input_dir = input("📁 请输入PNG文件所在目录: ").strip()
        if not input_dir or not os.path.isdir(input_dir):
            print("❌ 无效的目录路径")
            return
        
        output_dir = input("📁 请输入输出目录 (回车使用原目录): ").strip()
        if not output_dir:
            output_dir = None
        
        print("\n🎨 描边模式选择:")
        print("1. solid - 实心描边")
        print("2. gradient - 渐变描边")
        print("3. shadow - 阴影描边") 
        print("4. glow - 发光描边")
        
        mode_choice = input("请选择模式 (1-4, 默认1): ").strip()
        mode_map = {"1": "solid", "2": "gradient", "3": "shadow", "4": "glow"}
        mode = mode_map.get(mode_choice, "solid")
        
        # 创建批量工具并处理
        batch_tool = BatchBorderTool()
        
        process_kwargs = {
            'border_thickness': 3,
            'border_color': (255, 255, 255, 255),
            'border_mode': BorderMode(mode),
            'auto_thickness': True,
            'smooth_level': 2,
            'feather_radius': 1,
            'recursive': True,
            'overwrite': False,
            'auto_confirm': False
        }
        
        batch_tool.process_batch(input_dir, output_dir, max_workers=4, **process_kwargs)
        
    except Exception as e:
        print(f"❌ 批量处理失败: {e}")

def quick_test():
    """快速测试模式"""
    print("🧪 快速测试模式")
    
    # 查找测试图片
    test_files = ["描边前.png", "test.png", "sample.png"]
    test_file = None
    
    for file in test_files:
        if os.path.exists(file):
            test_file = file
            break
    
    if not test_file:
        print("❌ 未找到测试图片")
        print("请将PNG图片命名为 '描边前.png' 放在当前目录")
        return
    
    try:
        from 高级描边工具 import AdvancedBorderTool, BorderMode
        
        print(f"📸 使用测试图片: {test_file}")
        print("🔄 正在生成不同模式的描边效果...")
        
        tool = AdvancedBorderTool()
        modes = [BorderMode.SOLID, BorderMode.GRADIENT, BorderMode.SHADOW, BorderMode.GLOW]
        
        for mode in modes:
            output_name = f"快速测试_{mode.value}.png"
            print(f"   处理 {mode.value} 模式...")
            
            success = tool.process_image(
                test_file, 
                output_name,
                border_thickness=4,
                border_color=(255, 255, 255, 255),
                border_mode=mode,
                auto_thickness=True
            )
            
            if success:
                print(f"   ✅ {output_name}")
            else:
                print(f"   ❌ {mode.value} 模式处理失败")
        
        print("🎉 快速测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def show_documentation():
    """显示使用说明"""
    doc_file = "使用说明.md"
    
    if os.path.exists(doc_file):
        print("📖 正在打开使用说明文档...")
        try:
            # 尝试用默认程序打开
            if sys.platform.startswith('win'):
                os.startfile(doc_file)
            elif sys.platform.startswith('darwin'):
                os.system(f'open "{doc_file}"')
            else:
                os.system(f'xdg-open "{doc_file}"')
            print("✅ 使用说明已在默认程序中打开")
        except:
            # 如果无法打开，显示文件内容
            print("📄 使用说明内容:")
            with open(doc_file, 'r', encoding='utf-8') as f:
                content = f.read()
                # 只显示前50行
                lines = content.split('\n')[:50]
                print('\n'.join(lines))
                if len(content.split('\n')) > 50:
                    print("\n... (更多内容请查看 使用说明.md 文件)")
    else:
        print("❌ 使用说明文档不存在")

def main():
    """主函数"""
    # 检查依赖
    if not check_dependencies():
        return
    
    # 显示欢迎信息
    show_welcome()
    
    # 主循环
    while True:
        show_menu()
        
        try:
            choice = input("请选择 (1-6): ").strip()
            
            if choice == '1':
                if not launch_gui():
                    input("按回车键继续...")
            elif choice == '2':
                launch_cli()
            elif choice == '3':
                launch_batch()
                input("按回车键继续...")
            elif choice == '4':
                quick_test()
                input("按回车键继续...")
            elif choice == '5':
                show_documentation()
                input("按回车键继续...")
            elif choice == '6':
                print("👋 感谢使用PNG图片智能描边工具！")
                break
            else:
                print("❌ 无效选择，请输入1-6")
                
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")
            input("按回车键继续...")

if __name__ == "__main__":
    main()
