# PNG图片智能描边工具 - 使用说明

## 概述

这是一个专为已抠图PNG图片设计的智能描边工具，能够为透明背景的PNG图片添加完美的封闭描边效果。工具提供了多种描边模式和丰富的参数调节选项，适用于各种类型的抠图对象。

## 功能特点

### 🎯 核心功能
- **智能边缘检测**: 自动识别主体对象的边缘轮廓
- **完美封闭描边**: 生成无断点、无缺失的连续描边
- **保持透明背景**: 完全保留原图的透明特性
- **多种描边模式**: 支持实心、渐变、阴影、发光等效果

### 🛠️ 高级特性
- **自动厚度检测**: 根据图片复杂度智能推荐描边厚度
- **边缘平滑处理**: 多级平滑算法确保描边质量
- **羽化效果**: 可调节的边缘羽化，创造自然过渡
- **颜色自定义**: 支持任意RGB颜色的描边
- **批量处理**: 支持命令行批量处理多个文件

## 安装要求

### 系统要求
- Python 3.7 或更高版本
- Windows/macOS/Linux 系统

### 依赖库
```bash
pip install opencv-python pillow numpy matplotlib tkinter
```

## 使用方法

### 1. 图形界面模式（推荐）

运行GUI界面：
```bash
python 描边工具GUI.py
```

#### 界面说明：
1. **文件选择区域**
   - 输入图片：选择要处理的PNG图片
   - 输出图片：设置保存路径（可自动生成）

2. **描边参数区域**
   - **描边厚度**：1-10像素，控制描边的粗细
   - **自动检测最佳厚度**：让程序智能分析最适合的厚度
   - **描边颜色**：点击选择任意颜色
   - **描边模式**：
     - `solid`：实心描边（默认）
     - `gradient`：渐变描边
     - `shadow`：阴影描边
     - `glow`：发光描边
   - **平滑级别**：0-5级，数值越高边缘越平滑
   - **羽化半径**：0-5像素，创造柔和的边缘过渡

3. **预览功能**
   - 点击"预览效果"查看描边结果
   - 支持实时参数调整

4. **处理按钮**
   - **开始处理**：生成最终的描边图片
   - **重置参数**：恢复默认设置

### 2. 命令行模式

#### 基础用法：
```bash
python 高级描边工具.py input.png
```

#### 完整参数：
```bash
python 高级描边工具.py input.png -o output.png -t 4 -c 255 0 0 -m gradient --auto-thickness --smooth 3 --feather 2
```

#### 参数说明：
- `input.png`：输入文件路径
- `-o, --output`：输出文件路径
- `-t, --thickness`：描边厚度（1-10）
- `-c, --color`：RGB颜色值（如：255 0 0 表示红色）
- `-m, --mode`：描边模式（solid/gradient/shadow/glow）
- `--auto-thickness`：启用自动厚度检测
- `--smooth`：平滑级别（0-5）
- `--feather`：羽化半径（0-5）

### 3. 简单测试模式

直接运行工具进行测试：
```bash
python 智能描边工具.py
```
会自动处理当前目录下的"描边前.png"文件。

## 描边模式详解

### 1. 实心描边 (solid)
- **特点**：均匀的实色描边
- **适用**：大多数场景，简洁明了
- **效果**：在对象边缘添加指定颜色的实心边框

### 2. 渐变描边 (gradient)
- **特点**：从内到外的透明度渐变
- **适用**：需要柔和过渡效果的场景
- **效果**：描边边缘呈现渐变透明效果

### 3. 阴影描边 (shadow)
- **特点**：带有偏移的阴影效果
- **适用**：需要立体感的设计
- **效果**：在对象右下方创建阴影描边

### 4. 发光描边 (glow)
- **特点**：多层模糊的发光效果
- **适用**：需要醒目或梦幻效果的场景
- **效果**：创造柔和的外发光效果

## 最佳实践

### 参数选择建议

1. **简单图形**（如几何形状）
   - 厚度：2-3像素
   - 模式：solid
   - 平滑：1-2级

2. **复杂图形**（如人物、动物）
   - 厚度：3-5像素
   - 模式：gradient或glow
   - 平滑：2-3级
   - 羽化：1-2像素

3. **精细图形**（如文字、图标）
   - 启用自动厚度检测
   - 模式：solid
   - 平滑：3-4级

### 颜色选择建议

- **白色描边**：适用于深色背景
- **黑色描边**：适用于浅色背景
- **对比色描边**：突出主体对象
- **同色系描边**：创造和谐效果

## 常见问题

### Q: 描边出现断点怎么办？
A: 增加平滑级别，或者启用羽化效果。

### Q: 描边太粗或太细？
A: 调整厚度参数，或启用自动厚度检测。

### Q: 处理后图片变大了？
A: 这是正常的，描边需要额外空间。程序会自动扩展画布。

### Q: 支持哪些图片格式？
A: 主要支持PNG格式，特别是带透明通道的PNG图片。

### Q: 可以批量处理吗？
A: 可以通过命令行脚本实现批量处理。

## 技术原理

### 边缘检测算法
1. 提取PNG图片的Alpha通道
2. 创建二值化遮罩识别非透明区域
3. 使用形态学膨胀操作扩展边界
4. 计算描边区域（膨胀区域 - 原始区域）

### 平滑处理
1. 高斯模糊平滑边缘
2. 形态学闭运算填补空隙
3. 形态学开运算去除噪点

### 效果渲染
1. 根据模式计算描边透明度
2. 应用颜色和特效
3. 合成最终图片

## 更新日志

### v1.0.0
- 基础描边功能
- 支持厚度和颜色调节

### v2.0.0
- 新增GUI界面
- 支持多种描边模式
- 智能厚度检测
- 预览功能

## 技术支持

如有问题或建议，请通过以下方式联系：
- 查看代码注释了解实现细节
- 修改参数进行个性化调整
- 根据需要扩展新的描边模式

## 许可证

本工具仅供学习和个人使用。
